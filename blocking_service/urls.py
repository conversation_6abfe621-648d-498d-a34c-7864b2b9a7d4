from django.conf.urls import url
from blocking_service import views as block_service_views

urlpatterns = [
    url(
        r'^block/$',
        block_service_views.BlockListView.as_view(),
        name='block_list',
    ),
    url(
        r'^block/(?P<pk>\d+)/file$',
        block_service_views.BlockDetailFileView.as_view(),
        name='block_detail_file_view'
    ),
    url(
        r'block/create/$',
        block_service_views.BlockCreateView.as_view(),
        name='block_create',
    ),
    url(
        r'block/(?P<pk>\d+)/confirm/$',
        block_service_views.BlockConfirmView.as_view(),
        name='block_confirm',
    ),

    url(
        r'^unblock/person/$',
        block_service_views.UnblockPersonListView.as_view(),
        name='unblock_person_list',
    ),
    url(
        r'^unblock/person/(?P<pk>\d+)/file$',
        block_service_views.UnblockPersonDetailFileView.as_view(),
        name='unblock_person_detail_file_view'
    ),
    url(
        r'unblock/person/create/$',
        block_service_views.UnblockPersonCreateView.as_view(),
        name='unblock_person_create',
    ),
    url(
        r'unblock/person/(?P<pk>\d+)/confirm/$',
        block_service_views.UnblockPersonConfirmView.as_view(),
        name='unblock_person_confirm',
    ),


    url(
        r'^unblock/legal/$',
        block_service_views.UnblockLegalListView.as_view(),
        name='unblock_legal_list',
    ),
    url(
        r'^unblock/legal/(?P<pk>\d+)/file$',
        block_service_views.UnblockLegalDetailFileView.as_view(),
        name='unblock_legal_detail_file_view'
    ),
    url(
        r'unblock/legal/create/$',
        block_service_views.UnblockLegalCreateView.as_view(),
        name='unblock_legal_create',
    ),
    url(
        r'unblock/legal/(?P<pk>\d+)/confirm/$',
        block_service_views.UnblockLegalConfirmView.as_view(),
        name='unblock_legal_confirm',
    ),

    url(
        r'person/$',
        block_service_views.PersonListView.as_view(),
        name='person_list',
    ),
    url(
        r'person/(?P<pk>\d+)/$',
        block_service_views.PersonDetailView.as_view(),
        name='person_detail',
    ),

    url(
        r'legal/$',
        block_service_views.LegalListView.as_view(),
        name='legal_list',
    ),

    url(
        r'block-number-list-ajax/$',
        block_service_views.BlockNumberListAjaxView.as_view(),
        name='block_number_list_ajax',
    ),
]
