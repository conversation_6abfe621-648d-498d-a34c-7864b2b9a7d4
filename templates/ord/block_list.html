{% extends 'core/layout.html' %}

{% load static empty permissions django_tables2 bootstrap3 %}
{% block css %}
    <link href="{% static 'vendors/select2/css/select2.min.css' %}"
          rel="stylesheet">
{% endblock css %}

{% block js %}
    <script src="{% static 'vendors/select2/js/select2.full.min.js' %}"></script>
    <script>
        $(document).ready(function () {
            $(document).ready(function () {
                $(".select2_multiple").select2({
                    allowClear: true,
                    placeholder: "Выберите номера",
                    minimumInputLength: 3,
                    language: "ru",
                    ajax: {
                        url: "{% url 'ord:block_number_list_ajax' %}",
                        dataType: 'json',
                        delay: 250,
                        data: function (params) {
                            return {
                                q: params.term,
                                page: params.page,
                            };
                        },
                        processResults: function (data, params) {
                            params.page = params.page || 1;

                            return {
                                results: data.results,
                                pagination: {
                                    more: data.pagination.more
                                }
                            };
                        },
                        cache: true
                    }
                });
            });
        });
    </script>
{% endblock %}

{% block content %}
    <div class="x_panel">
        <div class="x_title">
            <h2>Список представлений</h2>
            <div class="clearfix"></div>
        </div>
        <div class="x_content">
            <div class="col-xs-12">
                <form>
                    {% bootstrap_form_errors filter.form %}
                    {% bootstrap_form filter.form form_group_class='form_group col-xs-6 col-md-4' %}
                    <div class="col-xs-12" style="margin-top: 10px;">
                        {% bootstrap_button content="Найти" button_type="submit" %}
                        <a href="{% url 'ord:block_list' %}"
                           class="btn btn-info">Сбросить</a>
                    </div>
                </form>
            </div>
            {% render_table table %}

            {% ifperms 'ord_block_create' %}
                <div class="row col-md-12">
                    <a href="{% url 'ord:block_create' %}"
                       class="btn btn-info right">
                        Добавить представление
                    </a>
                </div>
            {% endifperms %}
        </div>
    </div>
{% endblock content %}
